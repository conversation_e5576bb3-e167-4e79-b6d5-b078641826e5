import { runCell } from '@jupyterlab/notebook';

/**
 * 应用补丁以禁用 pendingInput 弹窗
 */
export function applyPendingInputPatch() {
  // 保存原始函数
  const originalRunCell = runCell;

  // 替换为修改后的版本
  (window as any)._originalRunCell = originalRunCell;

  // @ts-ignore - 覆盖原始函数
  runCell = async options => {
    // 创建一个新的选项对象，强制 pendingInput 为 false
    const patchedOptions = {
      ...options,
      sessionContext: options.sessionContext
        ? { ...options.sessionContext, pendingInput: false }
        : options.sessionContext,
    };

    return originalRunCell(patchedOptions);
  };
}
