import {
  runCell as originalRunCell,
  INotebookCellExecutor,
} from '@jupyterlab/notebook';

/**
 * 自定义的 runCell 函数，跳过 pendingInput 检查
 */
export async function runCell(
  options: INotebookCellExecutor.IRunCellOptions
): Promise<boolean> {
  // 创建一个代理对象，修改 sessionContext 属性
  const modifiedOptions = {
    ...options,
    sessionContext: {
      ...options.sessionContext,
      // 强制将 pendingInput 设为 false
      pendingInput: false,
    },
  };

  // 调用原始的 runCell 函数
  return originalRunCell(modifiedOptions);
}
